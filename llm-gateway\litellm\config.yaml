model_list:
  - model_name: secllm-v3 
    litellm_params:
      model: hosted_vllm/secllm-v3
      api_base: http://************:8080/v1
      api_key: sk-owl-secllm

  - model_name: secllm-v2
    litellm_params:
      model: hosted_vllm/secllm-v2
      api_base: http://***********:11035/v1
      api_key: sk-owl-secllm

  - model_name: secllm-v2-reason
    litellm_params:
      model: hosted_vllm/secllm-v2-reason
      api_base: http://************:11035/v1
      api_key: sk-owl-secllm

  - model_name: gpt-4o
    litellm_params: 
      model: openai/gpt-4o
      api_base: http://*************:3000/v1
      api_key: sk-NEfDPCzv37Iy5g3sA40e3e78327a46A6B748A41dC21fB102
  
  - model_name: deepseek-reasoner
    litellm_params:
      model: deepseek/deepseek-reasoner
      api_base: https://api.deepseek.com/v1
      api_key: ***********************************
  
  - model_name: deepseek-chat
    litellm_params:
      model: deepseek/deepseek-chat
      api_base: https://api.deepseek.com/v1
      api_key: ***********************************

  - model_name: secllm-v3-xdr
    litellm_params:
      model: hosted_vllm/secllm-v3
      api_base: http://************:8080/v1
      api_key: sk-owl-secllm



guardrails:
  - guardrail_name: "llm_tc260_guardrail"
    litellm_params:
      guardrail: llm_moderation_guardrail.LLMModerationGuardrail
      mode: ["post_call", "during_call"]



litellm_settings:
  # Logging/Callback settings
  # success_callback: ["langfuse"]  # list of success callbacks
  # failure_callback: ["sentry"]  # list of failure callbacks
  # callbacks: ["langfuse"]  # list of callbacks - runs on success and failure
  # service_callbacks: ["datadog", "prometheus"]  # logs redis, postgres failures on datadog, prometheus
  turn_off_message_logging: False  # prevent the messages and responses from being logged to on your callbacks, but request metadata will still be logged.
  redact_user_api_key_info: False  # Redact information about the user api key (hashed token, user_id, team id, etc.), from logs. Currently supported for Langfuse, OpenTelemetry, Logfire, ArizeAI logging.
  # langfuse_default_tags: ["cache_hit", "cache_key", "proxy_base_url", "user_api_key_alias", "user_api_key_user_id", "user_api_key_user_email", "user_api_key_team_alias", "semantic-similarity"] # default tags for Langfuse Logging
  
  # Networking settings
  request_timeout: 600 # (int) llm requesttimeout in seconds. Raise Timeout error if call takes longer than 10s. Sets litellm.request_timeout 
  # force_ipv4: boolean # If true, litellm will force ipv4 for all LLM requests. Some users have seen httpx ConnectionError when using ipv6 + Anthropic API
  
  set_verbose: True # sets litellm.set_verbose=True to view verbose debug logs. DO NOT LEAVE THIS ON IN PRODUCTION
  json_logs: True # if true, logs will be in json format

  # Fallbacks, reliability
  # default_fallbacks: ["langfuse"] # set default_fallbacks, in case a specific model group is misconfigured / bad.
  # content_policy_fallbacks: [{"gpt-3.5-turbo-small": ["claude-opus"]}] # fallbacks for ContentPolicyErrors
  # context_window_fallbacks: [{"gpt-3.5-turbo-small": ["gpt-3.5-turbo-large", "claude-opus"]}] # fallbacks for ContextWindowExceededErrors

  
  drop_params: False  # If a provider/model doesn't support a particular param, you can drop it.
  REPEATED_STREAMING_CHUNK_LIMIT: 4000 # catch if model starts looping the same chunk while streaming. Uses high default to prevent false positives.
  
  cache: True
  cache_params:
    type: redis
    host: **********
    port: 16379
    password: litellm210redis

router_settings:
  redis_host: **********
  redis_password: litellm210redis
  redis_port: 16379


environment_variables:
  # LANGFUSE_PUBLIC_KEY: "pk-lf-262a2af4-0280-41a6-b866-9b57d0a07153"
  # LANGFUSE_SECRET_KEY: "******************************************"
  # LANGFUSE_HOST: "http://**********:3000"
  LITELLM_MASTER_KEY: "sk-litellm-210-owl"
  DATABASE_URL: "*****************************************************/litellm"
  PORT: 4000
  UI_USERNAME: "owl-litellm-210"
  UI_PASSWORD: "owl#nsf0cus."
