services:
  litellm:
    image: ghcr.io/berriai/litellm:main-stable
    container_name: litellm
    volumes:
      - ./config.yaml:/app/config.yaml
      - ./llm_moderation_guardrail.py:/app/llm_moderation_guardrail.py
    environment:
      # - LANGFUSE_PUBLIC_KEY=pk-lf-262a2af4-0280-41a6-b866-9b57d0a07153
      # - LANGFUSE_SECRET_KEY=******************************************
      # - LANGFUSE_HOST=http://**********:3000
      - LITELLM_MASTER_KEY=sk-litellm-210-owl
      - DATABASE_URL=*****************************************************/litellm
      - UI_USERNAME=owl-litellm-210
      - UI_PASSWORD=owl#nsf0cus.
      - STORE_MODEL_IN_DB = True
    ports:
      - "18089:4000"
    command: --config /app/config.yaml --host 0.0.0.0 --port 4000 --num_workers 8 --detailed_debug
    depends_on:
      - litellm-pg
      - litellm-redis

  litellm-pg:
    image: postgres:15-alpine
    container_name: litellm-pg
    environment:
      - POSTGRES_USER=litellm
      - POSTGRES_PASSWORD=litellm210pgsql
      - POSTGRES_DB=litellm
    ports:
      - "15432:5432"
    volumes:
      - ./data/litellm-pg:/var/lib/postgresql/data

  litellm-redis:
    image: redis:7.4
    container_name: litellm-redis
    ports:
      - "16379:6379"
    volumes:
      - ./data/litellm-redis:/data
    command: redis-server --requirepass litellm210redis


